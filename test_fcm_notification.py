#!/usr/bin/env python3
"""
FCM Test Notification Sender for Pet House App
This script sends a test notification to the Pet House Android app using Firebase Cloud Messaging.
"""

import requests
import json
import sys

# Firebase Server Key (you'll need to get this from Firebase Console)
# Go to Firebase Console > Project Settings > Cloud Messaging > Server Key
# For testing purposes, we'll use a placeholder - replace with actual key
SERVER_KEY = "AAAA_test_key_replace_with_real_server_key"

# The FCM token we retrieved from the app
FCM_TOKEN = "e0dQwxZ6RKG4OjHWfD3wIg:APA91bFGQ-LzJBFQ2L5Q82lB8Vvcv5X3ffe_MpPDGPqD5li__qriXT3voGWPiV1_w0UOr_9rVTMNaBI31SS-7-gB_s_EUAc-GObN-ntXPjKeBUUoxvU8uz4"

def send_test_notification():
    """Send a test notification to the Pet House app"""
    
    # FCM endpoint
    url = "https://fcm.googleapis.com/fcm/send"
    
    # Headers
    headers = {
        "Authorization": f"key={SERVER_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test notification payload
    payload = {
        "to": FCM_TOKEN,
        "notification": {
            "title": "🐾 Pet House Test",
            "body": "FCM notifications are working! Your pet supplies are ready.",
            "icon": "ic_stat_ic_notification",
            "color": "#FCD83A"
        },
        "data": {
            "title": "Pet House Test",
            "body": "This is a test notification from Pet House app",
            "click_action": "FLUTTER_NOTIFICATION_CLICK",
            "test": "true"
        },
        "priority": "high",
        "android": {
            "notification": {
                "channel_id": "fcm_default_channel",
                "sound": "default",
                "priority": "high"
            }
        }
    }
    
    print("🚀 Sending test notification to Pet House app...")
    print(f"📱 Target Token: {FCM_TOKEN[:50]}...")
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Notification sent successfully!")
            print(f"📊 Response: {json.dumps(result, indent=2)}")
            
            if result.get('success') == 1:
                print("🎉 FCM confirmed delivery!")
            else:
                print("⚠️ FCM reported an issue:")
                print(f"   Results: {result.get('results', [])}")
                
        else:
            print(f"❌ Failed to send notification. Status: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"💥 Error sending notification: {e}")

def send_data_only_notification():
    """Send a data-only notification (no notification payload)"""
    
    url = "https://fcm.googleapis.com/fcm/send"
    
    headers = {
        "Authorization": f"key={SERVER_KEY}",
        "Content-Type": "application/json"
    }
    
    # Data-only payload (will be handled by onMessageReceived)
    payload = {
        "to": FCM_TOKEN,
        "data": {
            "title": "🔔 Pet House Data Message",
            "body": "This is a data-only message that should trigger onMessageReceived",
            "type": "test",
            "timestamp": str(int(requests.get("http://worldtimeapi.org/api/timezone/UTC").json()["unixtime"]))
        },
        "priority": "high"
    }
    
    print("📡 Sending data-only notification...")
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Data message sent successfully!")
            print(f"📊 Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Failed to send data message. Status: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"💥 Error sending data message: {e}")

if __name__ == "__main__":
    print("🐾 Pet House FCM Test Notification Sender")
    print("=" * 50)
    
    if SERVER_KEY == "YOUR_SERVER_KEY_HERE":
        print("❌ Please update the SERVER_KEY in this script with your Firebase Server Key")
        print("📖 Get it from: Firebase Console > Project Settings > Cloud Messaging > Server Key")
        sys.exit(1)
    
    print("1️⃣ Sending notification with notification payload...")
    send_test_notification()
    
    print("\n" + "=" * 50)
    
    print("2️⃣ Sending data-only message...")
    send_data_only_notification()
    
    print("\n🔍 Check the Android logs for FCM message reception:")
    print("   adb logcat -s MyFirebaseMsgService:* App:*")
