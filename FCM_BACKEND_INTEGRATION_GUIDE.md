# FCM Backend Integration Guide for Pet House App

## 🎯 Overview
This guide shows how to send FCM push notifications from your backend that will appear as **heads-up banners** on the Pet House Android app.

## 📱 Current FCM Token
Use this token for testing (regenerated on each app install):
```
e0dQwxZ6RKG4OjHWfD3wIg:APA91bFGQ-LzJBFQ2L5Q82lB8Vvcv5X3ffe_MpPDGPqD5li__qriXT3voGWPiV1_w0UOr_9rVTMNaBI31SS-7-gB_s_EUAc-GObN-ntXPjKeBUUoxvU8uz4
```

## 🔑 Firebase Configuration
1. **Server Key**: Get from Firebase Console → Project Settings → Cloud Messaging → Server Key
2. **Sender ID**: Found in Firebase Console → Project Settings → General → Project Number

## 📨 Backend Implementation Examples

### 1. Basic Heads-Up Notification (PHP)
```php
<?php
function sendPetHouseNotification($title, $message, $fcmToken, $isUrgent = false) {
    $serverKey = 'YOUR_FIREBASE_SERVER_KEY';
    
    $notification = [
        'title' => $title,
        'body' => $message,
        'sound' => 'default',
        'badge' => 1
    ];
    
    $data = [
        'title' => $title,
        'body' => $message,
        'urgent' => $isUrgent ? 'true' : 'false',
        'priority' => $isUrgent ? 'high' : 'normal',
        'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
    ];
    
    $payload = [
        'to' => $fcmToken,
        'notification' => $notification,
        'data' => $data,
        'priority' => 'high',
        'content_available' => true
    ];
    
    $headers = [
        'Authorization: key=' . $serverKey,
        'Content-Type: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($result, true);
}

// Usage Examples:
// Regular notification
sendPetHouseNotification(
    "🐾 New Order Update", 
    "Your pet food order has been shipped!", 
    $fcmToken
);

// Urgent notification (will use urgent channel)
sendPetHouseNotification(
    "🚨 URGENT: Delivery Alert", 
    "Your pet needs immediate attention - delivery arriving in 5 minutes!", 
    $fcmToken, 
    true
);
?>
```

### 2. Node.js Implementation
```javascript
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('./path/to/serviceAccountKey.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

async function sendPetHouseNotification(title, message, fcmToken, isUrgent = false) {
  const payload = {
    token: fcmToken,
    notification: {
      title: title,
      body: message
    },
    data: {
      title: title,
      body: message,
      urgent: isUrgent.toString(),
      priority: isUrgent ? 'high' : 'normal'
    },
    android: {
      priority: 'high',
      notification: {
        channelId: isUrgent ? 'pet_house_urgent' : 'pet_house_heads_up',
        priority: 'high',
        defaultSound: true,
        defaultVibrateTimings: true,
        defaultLightSettings: true
      }
    }
  };

  try {
    const response = await admin.messaging().send(payload);
    console.log('Successfully sent message:', response);
    return response;
  } catch (error) {
    console.log('Error sending message:', error);
    throw error;
  }
}

// Usage Examples:
sendPetHouseNotification(
  "🐾 Pet House", 
  "Your order is ready for pickup!", 
  fcmToken
);

sendPetHouseNotification(
  "🚨 URGENT: Pet Emergency", 
  "Emergency vet appointment confirmed for today at 3 PM", 
  fcmToken, 
  true
);
```

### 3. Python Implementation
```python
import requests
import json

def send_pet_house_notification(title, message, fcm_token, is_urgent=False):
    server_key = 'YOUR_FIREBASE_SERVER_KEY'
    
    headers = {
        'Authorization': f'key={server_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'to': fcm_token,
        'notification': {
            'title': title,
            'body': message,
            'sound': 'default'
        },
        'data': {
            'title': title,
            'body': message,
            'urgent': str(is_urgent).lower(),
            'priority': 'high' if is_urgent else 'normal'
        },
        'priority': 'high',
        'content_available': True
    }
    
    response = requests.post(
        'https://fcm.googleapis.com/fcm/send',
        headers=headers,
        data=json.dumps(payload)
    )
    
    return response.json()

# Usage Examples:
send_pet_house_notification(
    "🐾 Pet Care Reminder", 
    "Time for your pet's daily medication!", 
    fcm_token
)

send_pet_house_notification(
    "⚠️ URGENT: Vet Appointment", 
    "Emergency vet slot available - confirm within 10 minutes!", 
    fcm_token, 
    True
)
```

## 🎨 Notification Types

### Regular Heads-Up Notification
- Uses `pet_house_heads_up` channel
- `IMPORTANCE_HIGH` with standard vibration
- Appears as banner for 10 seconds

### Urgent Notification  
- Uses `pet_house_urgent` channel
- `IMPORTANCE_HIGH` with stronger vibration
- Can bypass Do Not Disturb
- Appears as banner for 15 seconds

## 🔧 Payload Structure

### Required Fields for Banner Display:
```json
{
  "to": "FCM_TOKEN",
  "notification": {
    "title": "Notification Title",
    "body": "Notification Message"
  },
  "data": {
    "title": "Notification Title",
    "body": "Notification Message",
    "urgent": "false",
    "priority": "normal"
  },
  "priority": "high"
}
```

### Optional Fields for Enhanced Experience:
```json
{
  "data": {
    "urgent": "true",           // Triggers urgent channel
    "priority": "high",         // High priority handling
    "action": "open_orders",    // Custom action
    "order_id": "12345",       // Additional data
    "deep_link": "pethouse://orders/12345"
  }
}
```

## 🧪 Testing Commands

Test the notifications using adb commands:

```bash
# Test regular FCM notification
adb shell am broadcast -a com.pet.house.app.TEST_NOTIFICATION -n com.pet.house.app/.receivers.NotificationTestReceiver

# Test heads-up banner
adb shell am broadcast -a com.pet.house.app.TEST_BANNER -n com.pet.house.app/.receivers.NotificationTestReceiver

# Test urgent notification
adb shell am broadcast -a com.pet.house.app.TEST_URGENT -n com.pet.house.app/.receivers.NotificationTestReceiver
```

## ✅ Verification

1. **Check Logs**: Monitor with `adb logcat | grep HeadsUpNotificationHelper`
2. **Channel Status**: Verify with `adb shell dumpsys notification | grep pet_house`
3. **Banner Appearance**: Notifications should slide down from top of screen
4. **Sound & Vibration**: Should play notification sound and vibrate

## 🚨 Troubleshooting

If banners don't appear:
1. Ensure app has notification permissions
2. Check device Do Not Disturb settings
3. Verify notification channels have `IMPORTANCE_HIGH`
4. Test on physical device (emulators may behave differently)
5. Ensure app is in background when testing

## 📋 Backend Checklist

- [ ] Firebase Server Key configured
- [ ] FCM token collection implemented
- [ ] Notification payload includes both `notification` and `data`
- [ ] `priority: "high"` set in payload
- [ ] `urgent` flag used for critical notifications
- [ ] Error handling for failed sends
- [ ] Token refresh handling
