#!/bin/bash

# Test Local Notification for Pet House App
# This script sends a test notification directly to the app using adb

echo "🐾 Testing Pet House App Notification Banner Display"
echo "=================================================="

# Check if app is installed
if ! adb shell pm list packages | grep -q "com.pet.house.app"; then
    echo "❌ Pet House app is not installed"
    exit 1
fi

echo "✅ Pet House app is installed"

# Put app in background (go to home)
echo "📱 Putting app in background..."
adb shell input keyevent KEYCODE_HOME
sleep 1

# Send a test notification using adb shell am broadcast
echo "📨 Sending test notification..."

# Method 1: Using am broadcast to trigger FCM service
adb shell am broadcast \
    -a com.google.firebase.messaging.RECEIVE \
    -n com.pet.house.app/com.pet.house.app.fcm.MyFirebaseMessagingService \
    --es "title" "🐾 Pet House Test" \
    --es "body" "This is a test notification banner!" \
    --es "from" "test" \
    --es "messageId" "test_$(date +%s)"

echo "✅ Test notification sent!"
echo ""
echo "🔍 Check your device screen for the notification banner"
echo "📋 If you don't see a banner, check notification settings:"
echo "   Settings > Apps > Pet House > Notifications"
echo ""
echo "📊 Monitor logs with:"
echo "   adb logcat -s MyFirebaseMsgService:* App:*"
