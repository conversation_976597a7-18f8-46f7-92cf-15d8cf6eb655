#!/bin/bash

# Direct Notification Test for Pet House App
echo "🐾 Testing Pet House App Notification Banner"
echo "============================================"

# Put app in background
echo "📱 Putting app in background..."
adb shell input keyevent KEYCODE_HOME
sleep 1

# Create a test notification using the notification manager directly
echo "📨 Creating test notification..."

# Use adb shell to create a notification directly
adb shell "
am start -a android.intent.action.MAIN -n com.pet.house.app/.ui.MainActivity --activity-clear-top --activity-single-top &
sleep 1
input keyevent KEYCODE_HOME
sleep 1

# Create notification using service call
service call notification 1 i32 10198 s16 'com.pet.house.app' i32 12345 s16 'test_tag' i32 0 i32 0 i32 0 i32 0 s16 '🐾 Pet House Test' s16 'This is a test notification banner!' i32 0 i32 0 i32 0 i32 0 i32 0
"

echo "✅ Test notification command sent!"
echo ""
echo "🔍 Check your device screen for the notification banner"
echo "📋 You should see a notification from Pet House app"
echo ""
echo "💡 If no banner appears, try:"
echo "   1. Check notification settings: Settings > Apps > Pet House > Notifications"
echo "   2. Ensure 'Show notifications' is enabled"
echo "   3. Check notification channel settings"
echo ""
echo "📊 Monitor logs with:"
echo "   adb logcat -s NotificationService:* NotificationManager:*"
