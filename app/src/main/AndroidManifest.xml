<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:fullBackupContent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:theme="@style/AppTheme"
        android:enableOnBackInvokedCallback="true">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Splash"
            android:windowSoftInputMode="stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.product.ProductsActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.product.ProductDetailActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.auth.RegisterActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.cart.CheckoutActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.grooming.GroomingBookingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.tag.TagDesignActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.myaccount.MyPersonalInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.myaccount.BillingAddressActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.myaccount.DeliveryAddressActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.myaccount.ChangePasswordActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.more.AboutUsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".ui.more.ServicesActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.more.PrivacyPolicyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.more.ReturnPolicyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.more.brand.OurBrandsActivity"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key" />

        <activity
            android:name=".ui.more.ContactUsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.more.delivary.DelivaryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.more.delivary.LostFoundActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity android:name=".ui.myaccount.OrderHistoryDetailsActivity" />
        <activity android:name=".ui.myaccount.FilterByBrandActivity" />
        <activity
            android:name=".ui.auth.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main" />
        <activity
            android:name=".ui.more.brand.BrandProductsActivity"
            android:label="@string/title_activity_brand_products"
            android:theme="@style/AppTheme" />
        <activity
            android:name=".ui.auth.ForgotPasswordActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Main"
            android:windowSoftInputMode="stateHidden" />
        <activity android:name=".ui.myaccount.oderList.OrderListActivity" />
        <activity android:name=".ui.home.search.SearchActivty" />
        <activity android:name=".ui.product.LocationActivity" />
        <activity android:name=".ui.grooming.GroomingPackageActivity" />
        <activity android:name=".ui.product.ProductImageActivity"/>
        <activity android:name=".ui.home.CategoryActivity" />

        <service
            android:name=".fcm.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
     See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_ic_notification" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/brand_primary_yellow" />

        <!-- Test notification receiver -->
        <receiver android:name=".receivers.NotificationTestReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.pet.house.app.TEST_NOTIFICATION" />
                <action android:name="com.pet.house.app.TEST_BANNER" />
                <action android:name="com.pet.house.app.TEST_URGENT" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
