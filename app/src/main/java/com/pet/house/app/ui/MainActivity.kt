package com.pet.house.app.ui

import com.pet.house.app.App
import com.pet.house.app.R
import com.pet.house.app.ui.base.BaseActivity
import com.pet.house.app.ui.base.BaseNavigationFragment
import com.pet.house.app.ui.cart.CartFragment
import com.pet.house.app.ui.home.CategoryActivity
import com.pet.house.app.ui.home.CategoryFragment
import com.pet.house.app.ui.home.HomeFragment
import com.pet.house.app.ui.more.MoreFragment
import com.pet.house.app.ui.myaccount.MyAccountNavigationFragment
import com.pet.house.app.ui.sale.SaleFragment
import com.pet.house.app.utils.AppUtils
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.pet.house.app.utils.NotificationTestHelper
import com.pet.house.app.fcm.MyFirebaseMessagingService
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.github.javiersantos.appupdater.AppUpdaterUtils
import com.github.javiersantos.appupdater.enums.AppUpdaterError
import com.github.javiersantos.appupdater.enums.UpdateFrom
import com.github.javiersantos.appupdater.objects.Update
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class MainActivity : BaseActivity() {

    private var currentFragment: BaseNavigationFragment? = null
    private lateinit var viewPager: ViewPager2
    private lateinit var bottomNavigation: BottomNavigationView

    companion object {
        private const val NOTIFICATION_PERMISSION_REQUEST_CODE = 1001
        var ARG_TAB: String = "tab";

        fun start(context: Context) {
            val starter = Intent(context, MainActivity::class.java)
            starter.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP;
            //        starter.putExtra();
            context.startActivity(starter)
        }

        fun startCart(context: Context) {
            val starter = Intent(context, MainActivity::class.java)
            // starter.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP; // Removed flags
            starter.putExtra(ARG_TAB, 2)
            context.startActivity(starter)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        initUI()

        // Add callback with proper priority for main activity
        onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (!handleBackPressed()) {
                        isEnabled = false
                        onBackPressedDispatcher.onBackPressed()
                    }
                }
            }
        )

        checkAppVersion()
        requestNotificationPermission()
    }

    override fun handleBackPressed(): Boolean {
        Log.d("MainActivity", "handleBackPressed called, current fragment: ${currentFragment?.javaClass?.simpleName}")
        val handledByFragment = currentFragment?.onBackPressed() ?: false
        
        if (handledByFragment) {
            Log.d("MainActivity", "Back press handled by current fragment")
            return true
        }
        
        // NEW LOGIC: Check if MainActivity was launched to show a specific tab
        // and if we are currently on that tab.
        val launchedForTab = intent.getIntExtra(ARG_TAB, -1) // Get the tab index we were launched for, default to -1 if not present
        if (launchedForTab != -1 && viewPager.currentItem == launchedForTab) {
            Log.d("MainActivity", "Launched for tab $launchedForTab and currently on it. Finishing MainActivity.")
            finish() // Finish MainActivity to go back to the previous activity in the OS stack
            return true // Back press is handled
        }
        
        // ORIGINAL LOGIC: If not on home, go to home, else show exit dialog
        when {
            viewPager.currentItem != 0 -> {
                Log.d("MainActivity", "Not on home tab, navigating to home")
                viewPager.currentItem = 0
                bottomNavigation.selectedItemId = R.id.navigation_home
                return true
            }
            else -> {
                Log.d("MainActivity", "On home tab, showing exit dialog")
                MaterialAlertDialogBuilder(this)
                    .setMessage(R.string.exit_app_message)
                    .setPositiveButton(R.string.yes) { _, _ -> finish() }
                    .setNegativeButton(R.string.no, null)
                    .show()
                return true
            }
        }
    }

    fun onCategoryClick(view: View) {
        var categoryId: Int? = 0

        when (view.id) {
            R.id.llDog -> categoryId = 1
            R.id.llCat -> categoryId = 2
            R.id.llBird -> categoryId = 3
            R.id.llSmallAnimals -> categoryId = 4
            R.id.llReptile -> categoryId = 5
            R.id.llFish -> categoryId = 6
        }


        CategoryActivity.start(this, categoryId!!)
    }

    private fun initUI() {
        setupBottomNavigation()
    }

    private fun setupBottomNavigation() {
        viewPager = findViewById(R.id.view_pager)
        bottomNavigation = findViewById(R.id.bottom_navigation)
        
        // Setup ViewPager
        val pagerAdapter = createAdapter()
        viewPager.adapter = pagerAdapter
        viewPager.isUserInputEnabled = false
        
        // Setup bottom navigation
        bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_home -> viewPager.setCurrentItem(0, false)
                R.id.navigation_sale -> viewPager.setCurrentItem(1, false)
                R.id.navigation_cart -> viewPager.setCurrentItem(2, false)
                R.id.navigation_my_account -> viewPager.setCurrentItem(3, false)
                R.id.navigation_more -> viewPager.setCurrentItem(4, false)
            }
            true
        }

        // Update current fragment when page changes
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                bottomNavigation.menu.getItem(position).isChecked = true
                
                // Get the FragmentManager's fragments
                val fragments = supportFragmentManager.fragments
                // Find the current fragment from ViewPager
                currentFragment = fragments.find { 
                    it is BaseNavigationFragment && 
                    fragments.indexOf(it) == position 
                } as? BaseNavigationFragment
            }
        })

        // Set initial fragment
        currentFragment = supportFragmentManager.fragments.firstOrNull { 
            it is BaseNavigationFragment 
        } as? BaseNavigationFragment
        
        // Handle ARG_TAB parameter to navigate to specific tab (like Cart)
        if (intent.hasExtra(ARG_TAB)) {
            val tabIndex = intent.getIntExtra(ARG_TAB, 0)
            viewPager.setCurrentItem(tabIndex, false)
            
            // Also update bottom navigation to show correct tab as selected
            when (tabIndex) {
                0 -> bottomNavigation.selectedItemId = R.id.navigation_home
                1 -> bottomNavigation.selectedItemId = R.id.navigation_sale
                2 -> bottomNavigation.selectedItemId = R.id.navigation_cart
                3 -> bottomNavigation.selectedItemId = R.id.navigation_my_account
                4 -> bottomNavigation.selectedItemId = R.id.navigation_more
            }
        }
    }
    
    private fun createAdapter(): FragmentStateAdapter {
        return object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = 5

            override fun createFragment(position: Int): Fragment {
                return when (position) {
                    0 -> HomeFragment()
                    1 -> SaleFragment()
                    2 -> CartFragment()
                    3 -> MyAccountNavigationFragment()
                    4 -> MoreFragment()
                    else -> throw IllegalStateException("Invalid position $position")
                }
            }
        }
    }

    private fun checkAppVersion() {
        if (App.instance?.shownVersionUpdate != true) {
            val appUpdater = AppUpdaterUtils(this)
                    .setUpdateFrom(UpdateFrom.JSON)
                    .setUpdateJSON(AppUtils.API_URL + "App_get_mobile_version.php")
                    .withListener(object : AppUpdaterUtils.UpdateListener {
                        override fun onSuccess(update: Update?, isUpdateAvailable: Boolean?) {
                            if (isUpdateAvailable == true) {
                                val materialDialog = MaterialAlertDialogBuilder(this@MainActivity)
                                    .setTitle(getString(R.string.version_update_title, update?.latestVersion))
                                    .setMessage(R.string.version_update_message)
                                    .setPositiveButton(R.string.version_update_update_buttom) { dialog, _ ->
                                        dialog.dismiss()
                                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(update?.urlToDownload.toString()))
                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        startActivity(intent)
                                    }
                                    .setNegativeButton(R.string.version_update_cancel_buttom) { dialog, _ ->
                                        dialog.dismiss()
                                    }
                                    .show()
                            }

                            App.instance?.shownVersionUpdate = true
                        }

                        override fun onFailed(error: AppUpdaterError?) {

                        }
                    })

            appUpdater.start()
        }
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {

                Log.d("MainActivity", "Requesting notification permission")
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                )
            } else {
                Log.d("MainActivity", "Notification permission already granted")
            }
        } else {
            Log.d("MainActivity", "Notification permission not required for this Android version")
        }
    }

    // Test method to trigger notification - can be called via adb
    fun testNotification() {
        Log.d("MainActivity", "testNotification() called")
        MyFirebaseMessagingService.sendTestNotificationStatic(this)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            NOTIFICATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d("MainActivity", "Notification permission granted")
                } else {
                    Log.d("MainActivity", "Notification permission denied")
                }
            }
        }
    }

    inner class NavigationAdapter(fm: FragmentManager) : FragmentPagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

        private val fragments = ArrayList<BaseNavigationFragment>()

        /**
         * Get the current fragment
         */
        var currentFragment: BaseNavigationFragment? = null
            private set

        init {

            fragments.clear()
            fragments.add(HomeFragment())
            fragments.add(SaleFragment())
            fragments.add(CartFragment())
            fragments.add(MyAccountNavigationFragment())
            fragments.add(MoreFragment())
        }

        override fun getItem(position: Int): BaseNavigationFragment {
            return fragments[position]
        }

        override fun getCount(): Int {
            return fragments.size
        }

        override fun setPrimaryItem(container: ViewGroup, position: Int, `object`: Any) {
            if (currentFragment !== `object`) {
                currentFragment = `object` as BaseNavigationFragment
            }

            super.setPrimaryItem(container, position, `object`)
        }

    }
}
