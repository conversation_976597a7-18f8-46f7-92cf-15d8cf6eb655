package com.pet.house.app.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.pet.house.app.R
import com.pet.house.app.ui.MainActivity

object HeadsUpNotificationHelper {
    private const val TAG = "HeadsUpNotificationHelper"
    private const val HEADS_UP_CHANNEL_ID = "pet_house_heads_up"
    private const val URGENT_CHANNEL_ID = "pet_house_urgent"
    
    fun createHeadsUpNotification(
        context: Context,
        title: String,
        message: String,
        isUrgent: Boolean = false
    ) {
        try {
            Log.d(TAG, "Creating heads-up notification: $title")
            
            // Create intent for when notification is tapped
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            
            val pendingIntent = PendingIntent.getActivity(
                context,
                System.currentTimeMillis().toInt(),
                intent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }
            )
            
            val channelId = if (isUrgent) URGENT_CHANNEL_ID else HEADS_UP_CHANNEL_ID
            val notificationManager = NotificationManagerCompat.from(context)
            
            // Create notification channels
            createNotificationChannels(context)
            
            // Custom vibration pattern for attention
            val vibrationPattern = longArrayOf(0, 300, 200, 300, 200, 300)
            
            // Build the notification with maximum priority and heads-up settings
            val notification = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.ic_stat_ic_notification)
                .setContentTitle(title)
                .setContentText(message)
                .setStyle(NotificationCompat.BigTextStyle().bigText(message))
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_HIGH) // Use HIGH instead of MAX to avoid aggressive behavior
                .setDefaults(0) // Clear all defaults to set custom behavior
                .setVibrate(vibrationPattern)
                .setLights(0xFFFF0000.toInt(), 500, 500) // Red light
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setOnlyAlertOnce(true) // Play sound only once per notification
                .setTimeoutAfter(10000) // Auto-dismiss after 10 seconds
                .setFullScreenIntent(pendingIntent, false) // Enable heads-up without full screen
                .setWhen(System.currentTimeMillis())
                .setShowWhen(true)
                .build()

            // Do NOT add FLAG_INSISTENT - it causes continuous sound looping
            
            val notificationId = System.currentTimeMillis().toInt()
            notificationManager.notify(notificationId, notification)
            
            Log.d(TAG, "Heads-up notification created with ID: $notificationId, Channel: $channelId")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating heads-up notification", e)
        }
    }
    
    private fun createNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Only create channels if they don't exist to avoid recreation
            val existingHeadsUpChannel = notificationManager.getNotificationChannel(HEADS_UP_CHANNEL_ID)
            val existingUrgentChannel = notificationManager.getNotificationChannel(URGENT_CHANNEL_ID)

            if (existingHeadsUpChannel == null) {
                // Create heads-up notification channel
                val headsUpChannel = NotificationChannel(
                    HEADS_UP_CHANNEL_ID,
                    "Pet House Heads-Up Notifications",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Important notifications that appear as banners"
                    enableLights(true)
                    lightColor = 0xFFFF0000.toInt()
                    enableVibration(true)
                    vibrationPattern = longArrayOf(0, 300, 200, 300, 200, 300)
                    setShowBadge(true)
                    lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                    setBypassDnd(false)

                    // Configure sound with proper audio attributes to prevent looping
                    val audioAttributes = AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                        .setFlags(AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                        .build()
                    setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION), audioAttributes)
                }

                notificationManager.createNotificationChannel(headsUpChannel)
                Log.d(TAG, "Created heads-up notification channel")
            }

            if (existingUrgentChannel == null) {
                // Create urgent notification channel
                val urgentChannel = NotificationChannel(
                    URGENT_CHANNEL_ID,
                    "Pet House Urgent Notifications",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Critical notifications that require immediate attention"
                    enableLights(true)
                    lightColor = 0xFFFF0000.toInt()
                    enableVibration(true)
                    vibrationPattern = longArrayOf(0, 500, 300, 500, 300, 500)
                    setShowBadge(true)
                    lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                    setBypassDnd(false) // Don't bypass DND to avoid aggressive behavior

                    // Configure sound with proper audio attributes to prevent looping
                    val audioAttributes = AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                        .setFlags(AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                        .build()
                    setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION), audioAttributes)
                }

                notificationManager.createNotificationChannel(urgentChannel)
                Log.d(TAG, "Created urgent notification channel")
            }
        }
    }
    
    fun sendTestBannerNotification(context: Context) {
        createHeadsUpNotification(
            context,
            "🚨 Pet House Banner Test",
            "This notification should appear as a sliding banner from the top of your screen! If you see this as a banner, the heads-up notification system is working correctly.",
            isUrgent = false
        )
    }
    
    fun sendUrgentTestNotification(context: Context) {
        createHeadsUpNotification(
            context,
            "⚠️ URGENT: Pet House Alert",
            "This is an urgent test notification that should definitely appear as a heads-up banner with vibration and sound!",
            isUrgent = true
        )
    }
}
