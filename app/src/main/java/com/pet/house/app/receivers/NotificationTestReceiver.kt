package com.pet.house.app.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.pet.house.app.fcm.MyFirebaseMessagingService

class NotificationTestReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "NotificationTestReceiver"
        const val ACTION_TEST_NOTIFICATION = "com.pet.house.app.TEST_NOTIFICATION"
    }
    
    override fun onReceive(context: Context?, intent: Intent?) {
        Log.d(TAG, "onReceive called with action: ${intent?.action}")
        
        if (intent?.action == ACTION_TEST_NOTIFICATION && context != null) {
            Log.d(TAG, "Triggering test notification")
            MyFirebaseMessagingService.sendTestNotificationStatic(context)
        }
    }
}
