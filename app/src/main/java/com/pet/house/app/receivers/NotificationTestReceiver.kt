package com.pet.house.app.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.pet.house.app.fcm.MyFirebaseMessagingService
import com.pet.house.app.utils.HeadsUpNotificationHelper

class NotificationTestReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "NotificationTestReceiver"
        const val ACTION_TEST_NOTIFICATION = "com.pet.house.app.TEST_NOTIFICATION"
        const val ACTION_TEST_BANNER = "com.pet.house.app.TEST_BANNER"
        const val ACTION_TEST_URGENT = "com.pet.house.app.TEST_URGENT"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        Log.d(TAG, "onReceive called with action: ${intent?.action}")

        if (context != null) {
            when (intent?.action) {
                ACTION_TEST_NOTIFICATION -> {
                    Log.d(TAG, "Triggering FCM test notification")
                    MyFirebaseMessagingService.sendTestNotificationStatic(context)
                }
                ACTION_TEST_BANNER -> {
                    Log.d(TAG, "Triggering heads-up banner test notification")
                    HeadsUpNotificationHelper.sendTestBannerNotification(context)
                }
                ACTION_TEST_URGENT -> {
                    Log.d(TAG, "Triggering urgent heads-up test notification")
                    HeadsUpNotificationHelper.sendUrgentTestNotification(context)
                }
            }
        }
    }
}
