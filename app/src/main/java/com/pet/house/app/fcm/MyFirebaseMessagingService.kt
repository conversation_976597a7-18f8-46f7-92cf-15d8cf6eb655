package com.pet.house.app.fcm

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.pet.house.app.R
import com.pet.house.app.ui.auth.LoginFragment
import com.pet.house.app.utils.HeadsUpNotificationHelper

class MyFirebaseMessagingService : FirebaseMessagingService() {
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "FCM Message received - From: ${remoteMessage.from}")
        Log.d(TAG, "FCM Message ID: ${remoteMessage.messageId}")

        // Process data payload if present
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "FCM Message data payload: ${remoteMessage.data}")
            handleNow(remoteMessage.data)
        }

        // Determine notification content and urgency
        val title: String
        val body: String
        val isUrgent: Boolean

        if (remoteMessage.notification != null) {
            // Handle notification payload
            Log.d(TAG, "FCM Notification payload found")
            title = remoteMessage.notification!!.title ?: "Pet House"
            body = remoteMessage.notification!!.body ?: ""

            // Check urgency from data or title content
            isUrgent = remoteMessage.data["urgent"]?.toBoolean() ?:
                      title.contains("URGENT", ignoreCase = true) ||
                      title.contains("ALERT", ignoreCase = true) ||
                      remoteMessage.data["priority"] == "high"
        } else if (remoteMessage.data.isNotEmpty()) {
            // Handle data-only payload
            Log.d(TAG, "FCM Data-only payload found")
            title = remoteMessage.data["title"] ?: "Pet House"
            body = remoteMessage.data["body"] ?: "You have a new message"

            // Check urgency from data
            isUrgent = remoteMessage.data["urgent"]?.toBoolean() ?:
                      remoteMessage.data["priority"] == "high" ||
                      title.contains("URGENT", ignoreCase = true)
        } else {
            Log.w(TAG, "FCM Message received with no notification or data payload")
            return
        }

        // Create single notification using HeadsUpNotificationHelper
        Log.d(TAG, "Creating FCM heads-up notification: $title (urgent: $isUrgent)")
        HeadsUpNotificationHelper.createHeadsUpNotification(
            context = this,
            title = title,
            message = body,
            isUrgent = isUrgent
        )
    }

    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        sendRegistrationToServer(token)
    }

    private fun handleNow(data: Map<String, String>) {
        // Handle the data message here
    }

    private fun sendRegistrationToServer(token: String) {
        // Send token to your server
    }



    companion object {
        private const val TAG = "MyFirebaseMsgService"

        // Static method to test heads-up notifications using HeadsUpNotificationHelper
        fun sendTestNotificationStatic(context: Context) {
            Log.d(TAG, "Creating test FCM notification using HeadsUpNotificationHelper")

            // Use the same helper to ensure consistent behavior
            HeadsUpNotificationHelper.createHeadsUpNotification(
                context = context,
                title = "🐾 FCM Test Notification",
                message = "This is a test FCM notification with proper sound configuration. Sound should play once and stop.",
                isUrgent = false
            )
        }
    }
}