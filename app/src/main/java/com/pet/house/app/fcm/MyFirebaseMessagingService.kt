package com.pet.house.app.fcm

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.pet.house.app.R
import com.pet.house.app.ui.auth.LoginFragment

class MyFirebaseMessagingService : FirebaseMessagingService() {
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "From: ${remoteMessage.from}")
        Log.d(TAG, "Message ID: ${remoteMessage.messageId}")

        remoteMessage.data.isNotEmpty().let {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            handleNow(remoteMessage.data)
        }

        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
            Log.d(TAG, "Message Notification Title: ${it.title}")
            sendNotification(it.title ?: "Pet House", it.body ?: "")
        }

        // If no notification payload, create one from data
        if (remoteMessage.notification == null && remoteMessage.data.isNotEmpty()) {
            val title = remoteMessage.data["title"] ?: "Pet House"
            val body = remoteMessage.data["body"] ?: "You have a new message"
            sendNotification(title, body)
        }
    }

    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        sendRegistrationToServer(token)
    }

    private fun handleNow(data: Map<String, String>) {
        // Handle the data message here
    }

    private fun sendRegistrationToServer(token: String) {
        // Send token to your server
    }

    private fun sendNotification(title: String, messageBody: String) {
        try {
            Log.d(TAG, "Creating heads-up notification with title: $title, body: $messageBody")

            val intent = Intent(this, com.pet.house.app.ui.MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
            val pendingIntent = PendingIntent.getActivity(
                this,
                0,
                intent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                else
                    PendingIntent.FLAG_UPDATE_CURRENT
            )

            val channelId = "fcm_heads_up_channel"
            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

            // Create vibration pattern for heads-up notifications
            val vibrationPattern = longArrayOf(0, 250, 250, 250)

            val notificationBuilder = NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.drawable.ic_stat_ic_notification)
                .setContentTitle(title)
                .setContentText(messageBody)
                .setStyle(NotificationCompat.BigTextStyle().bigText(messageBody))
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX) // Use PRIORITY_MAX for heads-up
                .setDefaults(0) // Clear defaults to set custom vibration
                .setVibrate(vibrationPattern)
                .setLights(0xFF0000FF.toInt(), 300, 100)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setOnlyAlertOnce(false) // Allow multiple alerts
                .setTimeoutAfter(10000) // Auto-dismiss after 10 seconds
                .setFullScreenIntent(pendingIntent, false)

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Create high-importance channel for heads-up notifications
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Delete old channel first
                notificationManager.deleteNotificationChannel("fcm_default_channel")

                val channel = NotificationChannel(
                    channelId,
                    "Pet House Heads-Up Notifications",
                    NotificationManager.IMPORTANCE_HIGH
                )
                channel.description = "Important notifications from Pet House app that appear as banners"
                channel.enableLights(true)
                channel.lightColor = 0xFF0000FF.toInt()
                channel.enableVibration(true)
                channel.vibrationPattern = vibrationPattern
                channel.setShowBadge(true)
                channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                channel.setBypassDnd(false)
                channel.canShowBadge()

                notificationManager.createNotificationChannel(channel)
                Log.d(TAG, "Created heads-up notification channel with IMPORTANCE_HIGH")
            }

            // Use unique notification ID to prevent notifications from being replaced
            val notificationId = System.currentTimeMillis().toInt()
            notificationManager.notify(notificationId, notificationBuilder.build())
            Log.d(TAG, "Heads-up notification sent successfully with ID: $notificationId")

        } catch (e: Exception) {
            Log.e(TAG, "Error creating heads-up notification", e)
        }
    }

    companion object {
        private const val TAG = "MyFirebaseMsgService"

        // Static method to test heads-up notifications
        fun sendTestNotificationStatic(context: Context) {
            try {
                Log.d(TAG, "Creating test heads-up notification from static method")

                val intent = Intent(context, com.pet.house.app.ui.MainActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                val pendingIntent = PendingIntent.getActivity(
                    context,
                    0,
                    intent,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                        PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                    else
                        PendingIntent.FLAG_UPDATE_CURRENT
                )

                val channelId = "fcm_heads_up_channel"
                val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

                // Create vibration pattern for heads-up notifications
                val vibrationPattern = longArrayOf(0, 250, 250, 250)

                val notificationBuilder = NotificationCompat.Builder(context, channelId)
                    .setSmallIcon(R.drawable.ic_stat_ic_notification)
                    .setContentTitle("🐾 Pet House Banner Test")
                    .setContentText("This notification should appear as a banner sliding down from the top!")
                    .setStyle(NotificationCompat.BigTextStyle().bigText("This notification should appear as a banner sliding down from the top! If you see this as a banner, heads-up notifications are working correctly."))
                    .setAutoCancel(true)
                    .setSound(defaultSoundUri)
                    .setContentIntent(pendingIntent)
                    .setPriority(NotificationCompat.PRIORITY_MAX) // Use PRIORITY_MAX for heads-up
                    .setDefaults(0) // Clear defaults to set custom vibration
                    .setVibrate(vibrationPattern)
                    .setLights(0xFF0000FF.toInt(), 300, 100)
                    .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                    .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                    .setOnlyAlertOnce(false) // Allow multiple alerts
                    .setTimeoutAfter(10000) // Auto-dismiss after 10 seconds
                    .setFullScreenIntent(pendingIntent, false)

                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

                // Create high-importance channel for heads-up notifications
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // Delete old channels first
                    notificationManager.deleteNotificationChannel("fcm_default_channel")

                    val channel = NotificationChannel(
                        channelId,
                        "Pet House Heads-Up Notifications",
                        NotificationManager.IMPORTANCE_HIGH
                    )
                    channel.description = "Important notifications from Pet House app that appear as banners"
                    channel.enableLights(true)
                    channel.lightColor = 0xFF0000FF.toInt()
                    channel.enableVibration(true)
                    channel.vibrationPattern = vibrationPattern
                    channel.setShowBadge(true)
                    channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                    channel.setBypassDnd(false)
                    channel.canShowBadge()

                    notificationManager.createNotificationChannel(channel)
                    Log.d(TAG, "Created test heads-up notification channel with IMPORTANCE_HIGH")
                }

                val notificationId = System.currentTimeMillis().toInt()
                notificationManager.notify(notificationId, notificationBuilder.build())
                Log.d(TAG, "Test heads-up notification sent successfully with ID: $notificationId")

            } catch (e: Exception) {
                Log.e(TAG, "Error creating test heads-up notification", e)
            }
        }
    }
}