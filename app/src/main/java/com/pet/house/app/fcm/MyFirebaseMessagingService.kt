package com.pet.house.app.fcm

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.pet.house.app.R
import com.pet.house.app.ui.auth.LoginFragment

class MyFirebaseMessagingService : FirebaseMessagingService() {
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "From: ${remoteMessage.from}")
        Log.d(TAG, "Message ID: ${remoteMessage.messageId}")

        remoteMessage.data.isNotEmpty().let {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            handleNow(remoteMessage.data)
        }

        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
            Log.d(TAG, "Message Notification Title: ${it.title}")
            sendNotification(it.title ?: "Pet House", it.body ?: "")
        }

        // If no notification payload, create one from data
        if (remoteMessage.notification == null && remoteMessage.data.isNotEmpty()) {
            val title = remoteMessage.data["title"] ?: "Pet House"
            val body = remoteMessage.data["body"] ?: "You have a new message"
            sendNotification(title, body)
        }
    }

    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        sendRegistrationToServer(token)
    }

    private fun handleNow(data: Map<String, String>) {
        // Handle the data message here
    }

    private fun sendRegistrationToServer(token: String) {
        // Send token to your server
    }

    private fun sendNotification(title: String, messageBody: String) {
        try {
            Log.d(TAG, "Creating notification with title: $title, body: $messageBody")
        val intent = Intent(this, com.pet.house.app.ui.MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        val pendingIntent = PendingIntent.getActivity(this, 0, intent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0)

        val channelId = "fcm_default_channel"
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.drawable.ic_stat_ic_notification)
                .setContentTitle(title)
                .setContentText(messageBody)
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setFullScreenIntent(pendingIntent, false)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(channelId,
                    "Pet House Notifications",
                    NotificationManager.IMPORTANCE_HIGH)
            channel.description = "Notifications from Pet House app"
            channel.enableLights(true)
            channel.enableVibration(true)
            channel.setShowBadge(true)
            channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            // Force the channel to be recreated with high importance
            notificationManager.deleteNotificationChannel(channelId)
            notificationManager.createNotificationChannel(channel)
        }

        // Use unique notification ID to prevent notifications from being replaced
        val notificationId = System.currentTimeMillis().toInt()
        notificationManager.notify(notificationId, notificationBuilder.build())
        Log.d(TAG, "Notification sent successfully with ID: $notificationId")

        } catch (e: Exception) {
            Log.e(TAG, "Error creating notification", e)
        }
    }

    companion object {
        private const val TAG = "MyFirebaseMsgService"

        // Static method to test notifications
        fun sendTestNotificationStatic(context: Context) {
            try {
                Log.d(TAG, "Creating test notification from static method")
                val intent = Intent(context, com.pet.house.app.ui.MainActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                val pendingIntent = PendingIntent.getActivity(context, 0, intent,
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0)

                val channelId = "fcm_default_channel"
                val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                val notificationBuilder = NotificationCompat.Builder(context, channelId)
                        .setSmallIcon(R.drawable.ic_stat_ic_notification)
                        .setContentTitle("🐾 Pet House Test")
                        .setContentText("This is a test notification banner!")
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent)
                        .setPriority(NotificationCompat.PRIORITY_HIGH)
                        .setDefaults(NotificationCompat.DEFAULT_ALL)
                        .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                        .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                        .setFullScreenIntent(pendingIntent, false)

                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val channel = NotificationChannel(channelId,
                            "Pet House Notifications",
                            NotificationManager.IMPORTANCE_HIGH)
                    channel.description = "Notifications from Pet House app"
                    channel.enableLights(true)
                    channel.enableVibration(true)
                    channel.setShowBadge(true)
                    channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                    notificationManager.createNotificationChannel(channel)
                }

                val notificationId = System.currentTimeMillis().toInt()
                notificationManager.notify(notificationId, notificationBuilder.build())
                Log.d(TAG, "Test notification sent successfully with ID: $notificationId")

            } catch (e: Exception) {
                Log.e(TAG, "Error creating test notification", e)
            }
        }
    }
}