#!/usr/bin/env python3
"""
Test script to simulate backend FCM push notifications for Pet House app
This script sends real FCM notifications that will trigger heads-up banners
"""

import requests
import json
import sys

# Firebase Configuration
SERVER_KEY = "YOUR_FIREBASE_SERVER_KEY_HERE"  # Replace with actual server key
FCM_TOKEN = "e0dQwxZ6RKG4OjHWfD3wIg:APA91bFGQ-LzJBFQ2L5Q82lB8Vvcv5X3ffe_MpPDGPqD5li__qriXT3voGWPiV1_w0UOr_9rVTMNaBI31SS-7-gB_s_EUAc-GObN-ntXPjKeBUUoxvU8uz4"

def send_fcm_notification(title, message, is_urgent=False, custom_data=None):
    """Send FCM notification that will appear as heads-up banner"""
    
    if SERVER_KEY == "YOUR_FIREBASE_SERVER_KEY_HERE":
        print("❌ Error: Please set your Firebase Server Key in the script")
        print("Get it from: Firebase Console → Project Settings → Cloud Messaging → Server Key")
        return False
    
    headers = {
        'Authorization': f'key={SERVER_KEY}',
        'Content-Type': 'application/json'
    }
    
    # Base data payload
    data = {
        'title': title,
        'body': message,
        'urgent': str(is_urgent).lower(),
        'priority': 'high' if is_urgent else 'normal',
        'timestamp': str(int(__import__('time').time()))
    }
    
    # Add custom data if provided
    if custom_data:
        data.update(custom_data)
    
    payload = {
        'to': FCM_TOKEN,
        'notification': {
            'title': title,
            'body': message,
            'sound': 'default',
            'badge': 1
        },
        'data': data,
        'priority': 'high',
        'content_available': True
    }
    
    print(f"📤 Sending {'URGENT' if is_urgent else 'NORMAL'} notification:")
    print(f"   Title: {title}")
    print(f"   Message: {message}")
    print(f"   Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            'https://fcm.googleapis.com/fcm/send',
            headers=headers,
            data=json.dumps(payload),
            timeout=10
        )
        
        result = response.json()
        
        if response.status_code == 200 and result.get('success', 0) > 0:
            print(f"✅ Notification sent successfully!")
            print(f"   Message ID: {result.get('results', [{}])[0].get('message_id', 'N/A')}")
            return True
        else:
            print(f"❌ Failed to send notification:")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {json.dumps(result, indent=2)}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending notification: {e}")
        return False

def main():
    print("🐾 Pet House FCM Backend Test Script")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
    else:
        print("Available test types:")
        print("1. normal    - Regular notification")
        print("2. urgent    - Urgent notification")
        print("3. order     - Order update notification")
        print("4. delivery  - Delivery notification")
        print("5. custom    - Custom notification")
        test_type = input("\nEnter test type (1-5 or name): ").lower()
    
    success = False
    
    if test_type in ['1', 'normal']:
        success = send_fcm_notification(
            "🐾 Pet House Update",
            "Your pet food order has been processed and will be shipped soon!",
            is_urgent=False
        )
    
    elif test_type in ['2', 'urgent']:
        success = send_fcm_notification(
            "🚨 URGENT: Pet House Alert",
            "Your delivery is arriving in 5 minutes! Please be ready to receive it.",
            is_urgent=True,
            custom_data={'action': 'track_delivery', 'delivery_id': '12345'}
        )
    
    elif test_type in ['3', 'order']:
        success = send_fcm_notification(
            "📦 Order Shipped",
            "Great news! Your pet supplies order #PH-2024-001 has been shipped and is on its way.",
            is_urgent=False,
            custom_data={'action': 'view_order', 'order_id': 'PH-2024-001'}
        )
    
    elif test_type in ['4', 'delivery']:
        success = send_fcm_notification(
            "🚚 Out for Delivery",
            "Your pet food delivery is out for delivery and will arrive between 2-4 PM today.",
            is_urgent=False,
            custom_data={'action': 'track_delivery', 'estimated_time': '2-4 PM'}
        )
    
    elif test_type in ['5', 'custom']:
        title = input("Enter notification title: ")
        message = input("Enter notification message: ")
        urgent = input("Is this urgent? (y/n): ").lower().startswith('y')
        
        success = send_fcm_notification(title, message, is_urgent=urgent)
    
    else:
        print("❌ Invalid test type")
        return
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("📱 Check your device for the heads-up notification banner")
        print("💡 The notification should slide down from the top of the screen")
    else:
        print("\n❌ Test failed. Please check your configuration.")

if __name__ == "__main__":
    main()
